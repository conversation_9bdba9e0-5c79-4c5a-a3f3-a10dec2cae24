import { Json } from "./common";

export type Services = {
	Row: {
		id: string;
		name: string;
		description: string | null;
		duration_minutes: number;
		buffer_time_minutes: number;
		base_price: number;
		max_participants: number;
		min_age: number | null;
		max_age: number | null;
		is_family_friendly: boolean;
		is_active: boolean;
		image_url: string | null;
		created_at: string | null;
		updated_at: string | null;
		features: Json;
		gallery: Json;
		location: string | null;
		category: string | null;
		default_employee_id: string | null;
		requires_qualification: boolean;
		auto_assign_employees: boolean;
	};
	Insert: Partial<Services["Row"]> & {
		name: string;
		duration_minutes: number;
		base_price: number;
		max_participants: number;
	};
	Update: Partial<Services["Row"]>;
};

export type PricingTiers = {
	Row: {
		id: string;
		service_id: string;
		tier_name: string;
		min_age: number;
		max_age: number | null;
		price: number;
		is_active: boolean;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: Partial<PricingTiers["Row"]> & {
		service_id: string;
		tier_name: string;
		price: number;
	};
	Update: Partial<PricingTiers["Row"]>;
};

export type ServiceSchedulingRules = {
	Row: {
		id: string;
		service_id: string | null;
		day_of_week: number | null;
		min_advance_booking_hours: number;
		max_advance_booking_days: number;
		operating_start_time: string | null;
		operating_end_time: string | null;
		booking_interval_minutes: number;
		specific_times: string[] | null;
		max_bookings_per_day: number | null;
		is_active: boolean;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: Partial<ServiceSchedulingRules["Row"]>;
	Update: Partial<ServiceSchedulingRules["Row"]>;
};

export type ScheduleTemplates = {
	Row: {
		id: string;
		name: string;
		description: string | null;
		service_id: string | null;
		template_data: Json;
		is_active: boolean;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: Partial<ScheduleTemplates["Row"]> & {
		name: string;
		template_data: Json;
	};
	Update: Partial<ScheduleTemplates["Row"]>;
};

export type ServiceAnalytics = {
	Row: {
		id: string;
		service_id: string;
		period_start: string;
		period_end: string;
		total_bookings: number;
		confirmed_bookings: number;
		cancelled_bookings: number;
		no_show_bookings: number;
		total_revenue: number;
		total_participants: number;
		average_group_size: number | null;
		occupancy_rate: number | null;
		average_rating: number | null;
		total_reviews: number;
		repeat_customer_rate: number | null;
		cancellation_rate: number | null;
		no_show_rate: number | null;
		peak_booking_hour: number | null;
		peak_booking_day: number | null;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: Partial<ServiceAnalytics["Row"]>;
	Update: Partial<ServiceAnalytics["Row"]>;
};

export type ServiceBlackoutDates = {
	Row: {
		id: string;
		service_id: string | null;
		start_date: string;
		end_date: string;
		reason: string | null;
		is_active: boolean;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: Partial<ServiceBlackoutDates["Row"]> & {
		start_date: string;
		end_date: string;
	};
	Update: Partial<ServiceBlackoutDates["Row"]>;
};

export type ServiceEquipmentRequirements = {
	Row: {
		id: string;
		service_id: string;
		equipment_id: string;
		capacity_per_participant: number;
	};
	Insert: Partial<ServiceEquipmentRequirements["Row"]> & {
		service_id: string;
		equipment_id: string;
	};
	Update: Partial<ServiceEquipmentRequirements["Row"]>;
};

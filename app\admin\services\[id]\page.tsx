"use client";

import AdminLayout from "@/components/admin/AdminLayout";
import ServiceBlackoutDates from "@/components/admin/ServiceBlackoutDates";
import ServiceSchedulingRules from "@/components/admin/ServiceSchedulingRules";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { adminApi } from "@/lib/api-client";
import { AlertTriangle, ArrowLeft, Clock, Settings } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface Service {
	id: string;
	name: string;
	description: string;
	duration_minutes: number;
	max_participants: number;
	price_adult: number;
	price_child: number;
	price_senior: number;
	is_active: boolean;
}

export default function ServiceDetailPage() {
	const params = useParams();
	const router = useRouter();
	const serviceId = params.id as string;

	const [service, setService] = useState<Service | null>(null);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		fetchService();
	}, [serviceId]);

	const fetchService = async () => {
		try {
			const data = await adminApi.getService(serviceId);
			setService(data.service);
		} catch (error) {
			console.error("Error fetching service:", error);
		} finally {
			setLoading(false);
		}
	};

	if (loading) {
		return <div className="p-6">Chargement...</div>;
	}

	if (!service) {
		return (
			<div className="p-6">
				<div className="text-center">
					<h1 className="text-2xl font-bold text-gray-900 mb-4">Service non trouvé</h1>
					<Button onClick={() => router.push("/admin/services")}>Retour aux services</Button>
				</div>
			</div>
		);
	}

	return (
		<AdminLayout>
			<div className="space-y-6">
				{/* Header */}
				<div className="flex items-center gap-4">
					<Button
						variant="outline"
						onClick={() => router.push("/admin/services")}
						className="flex items-center gap-2"
					>
						<ArrowLeft className="w-4 h-4" />
						Retour
					</Button>
					<div>
						<h1 className="text-2xl font-bold text-gray-900">{service.name}</h1>
						<p className="text-gray-600">Gestion des paramètres de disponibilité</p>
					</div>
				</div>

				{/* Service Info Card */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Settings className="w-5 h-5" />
							Informations du service
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
							<div>
								<label className="text-sm font-medium text-gray-500">Durée</label>
								<p className="text-lg font-semibold">{service.duration_minutes} min</p>
							</div>
							<div>
								<label className="text-sm font-medium text-gray-500">Participants max</label>
								<p className="text-lg font-semibold">{service.max_participants}</p>
							</div>
							<div>
								<label className="text-sm font-medium text-gray-500">Prix adulte</label>
								<p className="text-lg font-semibold">{service.price_adult}€</p>
							</div>
							<div>
								<label className="text-sm font-medium text-gray-500">Statut</label>
								<p
									className={`text-lg font-semibold ${
										service.is_active ? "text-green-600" : "text-red-600"
									}`}
								>
									{service.is_active ? "Actif" : "Inactif"}
								</p>
							</div>
						</div>
						{service.description && (
							<div className="mt-4">
								<label className="text-sm font-medium text-gray-500">Description</label>
								<p className="text-gray-700 mt-1">{service.description}</p>
							</div>
						)}
					</CardContent>
				</Card>

				{/* Availability Management Tabs */}
				<Tabs defaultValue="scheduling" className="space-y-6">
					<TabsList className="grid w-full grid-cols-2">
						<TabsTrigger value="scheduling" className="flex items-center gap-2">
							<Clock className="w-4 h-4" />
							Règles de planification
						</TabsTrigger>
						<TabsTrigger value="blackouts" className="flex items-center gap-2">
							<AlertTriangle className="w-4 h-4" />
							Périodes d'indisponibilité
						</TabsTrigger>
					</TabsList>

					<TabsContent value="scheduling">
						<ServiceSchedulingRules serviceId={serviceId} serviceName={service.name} />
					</TabsContent>

					<TabsContent value="blackouts">
						<ServiceBlackoutDates serviceId={serviceId} serviceName={service.name} />
					</TabsContent>
				</Tabs>
			</div>
		</AdminLayout>
	);
}
